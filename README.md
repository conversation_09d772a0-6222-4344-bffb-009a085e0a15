# Mobile UI Components Library

基于 Figma 设计稿创建的移动端 UI 组件库，使用 Vue 3 + Vite 构建。

## 🎨 设计来源

本组件库基于 [Mobile Apps – Prototyping Kit (Community)](https://www.figma.com/design/KLzu8sFVr2T8rjURqE7bKG/Mobile-Apps-%E2%80%93-Prototyping-Kit--Community-) Figma 设计稿创建。

## 🚀 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## 📦 组件列表

### 基础组件

- **Button** - 按钮组件，支持 primary、secondary、tertiary 三种变体
- **Input** - 输入框组件，支持多种状态和验证
- **Avatar** - 头像组件，支持图片、姓名首字母和占位符
- **Tag** - 标签组件，支持多种变体、可关闭和可点击

### 表单组件

- **Checkbox** - 复选框组件，支持多种尺寸
- **Toggle** - 开关组件，支持启用/禁用状态
- **Slider** - 滑块组件，支持范围选择和数值显示
- **NumberInput** - 数字输入组件，支持步进按钮和范围限制
- **Rating** - 星级评分组件，支持只读和交互模式

### 布局组件

- **Card** - 卡片组件，支持垂直和水平布局
- **TabBar** - 标签栏组件，支持徽章显示
- **ListItem** - 列表项组件，支持头像、图标、徽章和开关
- **Accordion** - 手风琴组件，支持单选和多选展开模式

### 反馈组件

- **Badge** - 徽章组件，支持多种变体和点状显示
- **Banner** - 横幅通知组件，支持多种状态和可关闭
- **Dialog** - 对话框组件，支持自定义内容和操作按钮
- **LoadingSpinner** - 加载动画组件，支持多种尺寸和文本

### 进度组件

- **ProgressBar** - 进度条组件，支持标签和百分比显示
- **Stepper** - 步骤指示器组件，支持垂直和水平布局
- **PaginationDots** - 分页点组件，支持多种尺寸和交互

### 消息组件

- **MessageBubble** - 消息气泡组件，支持发送和接收样式

## 🎯 组件特性

- 🎨 基于真实设计稿创建，保证设计一致性
- 📱 移动端优先，响应式设计
- ♿ 支持无障碍访问
- 🎭 支持多种主题变体
- 🔧 TypeScript 友好（计划中）
- 📚 完整的文档和示例

## 💡 使用示例

```vue
<template>
  <div>
    <!-- 按钮示例 -->
    <Button variant="primary" @click="handleClick">
      Primary Button
    </Button>

    <!-- 输入框示例 -->
    <Input
      v-model="inputValue"
      label="Email"
      placeholder="Enter your email"
    />

    <!-- 卡片示例 -->
    <Card
      title="Card Title"
      subtitle="Card subtitle"
      :clickable="true"
    >
      Card content goes here
    </Card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Button, Input, Card } from './components/ui'

const inputValue = ref('')

const handleClick = () => {
  console.log('Button clicked!')
}
</script>
```

## 🛠 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **CSS3** - 现代 CSS 特性
- **ES6+** - 现代 JavaScript 语法

## 📁 项目结构

```
src/
├── components/
│   └── ui/
│       ├── Button/
│       ├── Input/
│       ├── Avatar/
│       ├── Card/
│       ├── Checkbox/
│       ├── Toggle/
│       ├── Badge/
│       ├── TabBar/
│       ├── MessageBubble/
│       ├── ProgressBar/
│       ├── LoadingSpinner/
│       ├── Slider/
│       ├── Rating/
│       ├── Stepper/
│       ├── Banner/
│       ├── Dialog/
│       ├── ListItem/
│       ├── Tag/
│       ├── NumberInput/
│       ├── Accordion/
│       ├── PaginationDots/
│       └── index.js
├── App.vue
└── main.js
```

## 🎨 设计系统

### 颜色规范

- **Primary**: #007AFF (iOS Blue)
- **Success**: #34C759 (iOS Green)
- **Warning**: #FF9500 (iOS Orange)
- **Error**: #FF3B30 (iOS Red)
- **Text**: #1C1C1E (iOS Label)
- **Secondary Text**: #8E8E93 (iOS Secondary Label)
- **Background**: #F2F2F7 (iOS System Background)

### 字体规范

- **Font Family**: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
- **Font Weights**: 400 (Regular), 500 (Medium), 600 (Semibold), 700 (Bold)

### 间距规范

- **Small**: 8px
- **Medium**: 16px
- **Large**: 24px
- **XLarge**: 32px

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
