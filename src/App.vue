<script setup>
import { ref } from 'vue'
import {
  Button,
  Input,
  Avatar,
  Card,
  Checkbox,
  Toggle,
  Badge,
  TabBar,
  MessageBubble,
  ProgressBar,
  LoadingSpinner,
  <PERSON>lider,
  Rating,
  Stepper,
  Banner,
  Dialog,
  ListItem,
  Tag,
  NumberInput,
  Accordion,
  PaginationDots
} from './components/ui'
import { StarIcon, HeartIcon } from '@heroicons/vue/24/solid'

// Component states
const inputValue = ref('')
const checkboxValue = ref(false)
const toggleValue = ref(false)
const selectedTab = ref(0)
const sliderValue = ref(50)
const ratingValue = ref(3)
const numberValue = ref(10)
const currentStep = ref(1)
const showDialog = ref(false)
const currentPage = ref(0)

const accordionItems = [
  { title: 'What is Vue.js?', content: 'Vue.js is a progressive JavaScript framework for building user interfaces.' },
  { title: 'How to install Vue?', content: 'You can install Vue using npm: npm install vue' },
  { title: 'What are components?', content: 'Components are reusable Vue instances with a name.' }
]

const tabs = [
  { label: 'Home', id: 'home' },
  { label: 'Search', id: 'search' },
  { label: 'Profile', id: 'profile', badge: 3 },
  { label: 'Settings', id: 'settings' }
]

const steps = [
  { title: 'Personal Info', description: 'Enter your basic information' },
  { title: 'Preferences', description: 'Set your preferences' },
  { title: 'Review', description: 'Review and confirm' },
  { title: 'Complete', description: 'Setup complete' }
]

const handleCardClick = () => {
  alert('Card clicked!')
}
</script>

<template>
  <div class="app">
    <header class="app-header">
      <h1>Mobile UI Components</h1>
      <p>Based on Figma Design System</p>
    </header>

    <main class="app-main">
      <!-- Buttons Section -->
      <section class="component-section">
        <h2>Buttons</h2>
        <div class="component-grid">
          <Button variant="primary">Primary Button</Button>
          <Button variant="secondary">Secondary Button</Button>
          <Button variant="tertiary">Tertiary Button</Button>
          <Button variant="primary" size="small">Small</Button>
          <Button variant="primary" size="large">Large</Button>
          <Button variant="primary" disabled>Disabled</Button>
        </div>
      </section>

      <!-- Inputs Section -->
      <section class="component-section">
        <h2>Input Fields</h2>
        <div class="component-grid">
          <Input
            v-model="inputValue"
            label="Email"
            placeholder="Enter your email"
            helper="We'll never share your email"
          />
          <Input
            label="Password"
            type="password"
            placeholder="Enter password"
          />
          <Input
            label="Error State"
            placeholder="This field has an error"
            error="This field is required"
          />
        </div>
      </section>

      <!-- Avatars Section -->
      <section class="component-section">
        <h2>Avatars</h2>
        <div class="component-grid">
          <Avatar size="small" name="John Doe" />
          <Avatar size="medium" name="Jane Smith" />
          <Avatar size="large" name="Bob Johnson" />
          <Avatar size="medium" />
        </div>
      </section>

      <!-- Cards Section -->
      <section class="component-section">
        <h2>Cards</h2>
        <div class="component-grid">
          <Card
            variant="vertical"
            title="Vertical Card"
            subtitle="This is a subtitle"
            :clickable="true"
            @click="handleCardClick"
          >
            <p>This is the card content. It can contain any information you want to display.</p>
            <template #footer>
              <Button variant="primary" size="small">Action</Button>
            </template>
          </Card>

          <Card
            variant="horizontal"
            title="Horizontal Card"
            subtitle="Side by side layout"
          >
            <p>Horizontal cards are great for list items.</p>
            <template #action>
              <Button variant="tertiary" size="small">→</Button>
            </template>
          </Card>
        </div>
      </section>

      <!-- Form Controls Section -->
      <section class="component-section">
        <h2>Form Controls</h2>
        <div class="component-grid">
          <Checkbox
            v-model="checkboxValue"
            label="I agree to the terms and conditions"
          />
          <Toggle
            v-model="toggleValue"
            label="Enable notifications"
          />
        </div>
      </section>

      <!-- Badges Section -->
      <section class="component-section">
        <h2>Badges</h2>
        <div class="component-grid">
          <Badge content="New" variant="primary" />
          <Badge content="5" variant="error" />
          <Badge content="Success" variant="success" />
          <Badge variant="warning" dot />
          <Badge content="Info" variant="info" size="large" />
        </div>
      </section>

      <!-- Tab Bar Section -->
      <section class="component-section">
        <h2>Tab Bar</h2>
        <div class="component-grid">
          <TabBar
            v-model="selectedTab"
            :tabs="tabs"
            @tab-change="(data) => console.log('Tab changed:', data)"
          />
        </div>
      </section>

      <!-- Message Bubbles Section -->
      <section class="component-section">
        <h2>Message Bubbles</h2>
        <div class="component-grid" style="flex-direction: column; max-width: 400px;">
          <MessageBubble type="received" message="Hello! How are you doing today?" />
          <MessageBubble type="sent" message="I'm doing great, thanks for asking!" />
          <MessageBubble type="received" message="That's wonderful to hear!" show-time time="2:30 PM" />
        </div>
      </section>

      <!-- Progress & Loading Section -->
      <section class="component-section">
        <h2>Progress & Loading</h2>
        <div class="component-grid" style="flex-direction: column; gap: 24px;">
          <ProgressBar :progress="75" label="Download Progress" show-percentage />
          <ProgressBar :progress="45" color="#34C759" />
          <div style="display: flex; gap: 16px; align-items: center;">
            <LoadingSpinner size="small" />
            <LoadingSpinner size="medium" text="Loading..." />
            <LoadingSpinner size="large" text="Please wait" />
          </div>
        </div>
      </section>

      <!-- Slider Section -->
      <section class="component-section">
        <h2>Slider</h2>
        <div class="component-grid" style="flex-direction: column; gap: 16px;">
          <Slider
            v-model="sliderValue"
            label="Volume"
            :min="0"
            :max="100"
            show-value
            show-min-max
          />
          <Slider
            :model-value="25"
            label="Brightness"
            :min="0"
            :max="100"
          />
        </div>
      </section>

      <!-- Rating Section -->
      <section class="component-section">
        <h2>Rating</h2>
        <div class="component-grid">
          <Rating v-model="ratingValue" show-text />
          <Rating :model-value="4" size="small" readonly />
          <Rating :model-value="5" size="large" />
        </div>
      </section>

      <!-- Stepper Section -->
      <section class="component-section">
        <h2>Stepper</h2>
        <div class="component-grid">
          <Stepper :steps="steps" :current-step="currentStep" />
        </div>
      </section>

      <!-- Banner Section -->
      <section class="component-section">
        <h2>Banner</h2>
        <div class="component-grid" style="flex-direction: column; gap: 12px;">
          <Banner variant="info" title="Information" message="This is an informational message." />
          <Banner variant="success" message="Operation completed successfully!" />
          <Banner variant="warning" message="Please check your input." />
          <Banner variant="error" title="Error" message="Something went wrong." />
        </div>
      </section>

      <!-- Number Input Section -->
      <section class="component-section">
        <h2>Number Input</h2>
        <div class="component-grid">
          <NumberInput
            v-model="numberValue"
            label="Quantity"
            :min="1"
            :max="100"
            show-value
          />
          <NumberInput
            :model-value="5"
            label="Rating"
            :min="1"
            :max="10"
            size="small"
          />
        </div>
      </section>

      <!-- Tags Section -->
      <section class="component-section">
        <h2>Tags</h2>
        <div class="component-grid">
          <Tag text="Default" />
          <Tag text="Primary" variant="primary" />
          <Tag text="Success" variant="success" />
          <Tag text="Warning" variant="warning" />
          <Tag text="Error" variant="error" />
          <Tag text="Closable" closable @close="() => console.log('Tag closed')" />
          <Tag text="Clickable" clickable @click="() => console.log('Tag clicked')" />
          <Tag text="Favorite" variant="primary" :icon="HeartIcon" />
          <Tag text="Featured" variant="success" :icon="StarIcon" />
        </div>
      </section>

      <!-- List Items Section -->
      <section class="component-section">
        <h2>List Items</h2>
        <div class="component-grid" style="flex-direction: column; gap: 0;">
          <ListItem
            title="Profile Settings"
            subtitle="Manage your account"
            show-arrow
            clickable
            @click="() => console.log('Profile clicked')"
          />
          <ListItem
            title="Notifications"
            subtitle="Push notifications"
            :toggle="true"
            @toggle="(value) => console.log('Toggle:', value)"
          />
          <ListItem
            title="Messages"
            subtitle="3 unread"
            :badge="3"
            show-arrow
            clickable
          />
          <ListItem
            title="John Doe"
            subtitle="Online"
            avatar-name="John Doe"
            show-arrow
            clickable
          />
        </div>
      </section>

      <!-- Dialog Section -->
      <section class="component-section">
        <h2>Dialog</h2>
        <div class="component-grid">
          <Button variant="primary" @click="showDialog = true">
            Show Dialog
          </Button>
          <Dialog
            v-model="showDialog"
            title="Confirm Action"
            message="Are you sure you want to proceed with this action?"
            @confirm="() => console.log('Confirmed')"
            @cancel="() => console.log('Cancelled')"
          />
        </div>
      </section>

      <!-- Accordion Section -->
      <section class="component-section">
        <h2>Accordion</h2>
        <div class="component-grid">
          <Accordion
            :items="accordionItems"
            @toggle="(data) => console.log('Accordion toggle:', data)"
          />
        </div>
      </section>

      <!-- Pagination Dots Section -->
      <section class="component-section">
        <h2>Pagination Dots</h2>
        <div class="component-grid" style="flex-direction: column; gap: 16px; align-items: center;">
          <PaginationDots
            :total="5"
            v-model:current="currentPage"
            @change="(page) => currentPage = page"
          />
          <div style="display: flex; gap: 16px;">
            <PaginationDots :total="3" :current="1" size="small" />
            <PaginationDots :total="4" :current="2" size="large" />
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  background-color: #FFFFFF;
  padding: 32px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1C1C1E;
}

.app-header p {
  margin: 0;
  font-size: 16px;
  color: #8E8E93;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px;
}

.component-section {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.component-section h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1C1C1E;
  border-bottom: 2px solid #F2F2F7;
  padding-bottom: 12px;
}

.component-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.component-grid > * {
  flex: 0 0 auto;
}

/* Responsive */
@media (max-width: 768px) {
  .app-main {
    padding: 16px;
  }

  .component-section {
    padding: 16px;
  }

  .component-grid {
    flex-direction: column;
  }

  .component-grid > * {
    width: 100%;
  }
}
</style>
