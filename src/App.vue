<script setup>
import { ref } from 'vue'
import {
  Button,
  Input,
  Avatar,
  Card,
  Checkbox,
  Toggle,
  Badge,
  TabBar
} from './components/ui'

// Component states
const inputValue = ref('')
const checkboxValue = ref(false)
const toggleValue = ref(false)
const selectedTab = ref(0)

const tabs = [
  { label: 'Home', id: 'home' },
  { label: 'Search', id: 'search' },
  { label: 'Profile', id: 'profile', badge: 3 },
  { label: 'Settings', id: 'settings' }
]

const handleCardClick = () => {
  alert('Card clicked!')
}
</script>

<template>
  <div class="app">
    <header class="app-header">
      <h1>Mobile UI Components</h1>
      <p>Based on Figma Design System</p>
    </header>

    <main class="app-main">
      <!-- Buttons Section -->
      <section class="component-section">
        <h2>Buttons</h2>
        <div class="component-grid">
          <Button variant="primary">Primary Button</Button>
          <Button variant="secondary">Secondary Button</Button>
          <Button variant="tertiary">Tertiary Button</Button>
          <Button variant="primary" size="small">Small</Button>
          <Button variant="primary" size="large">Large</Button>
          <Button variant="primary" disabled>Disabled</Button>
        </div>
      </section>

      <!-- Inputs Section -->
      <section class="component-section">
        <h2>Input Fields</h2>
        <div class="component-grid">
          <Input
            v-model="inputValue"
            label="Email"
            placeholder="Enter your email"
            helper="We'll never share your email"
          />
          <Input
            label="Password"
            type="password"
            placeholder="Enter password"
          />
          <Input
            label="Error State"
            placeholder="This field has an error"
            error="This field is required"
          />
        </div>
      </section>

      <!-- Avatars Section -->
      <section class="component-section">
        <h2>Avatars</h2>
        <div class="component-grid">
          <Avatar size="small" name="John Doe" />
          <Avatar size="medium" name="Jane Smith" />
          <Avatar size="large" name="Bob Johnson" />
          <Avatar size="medium" />
        </div>
      </section>

      <!-- Cards Section -->
      <section class="component-section">
        <h2>Cards</h2>
        <div class="component-grid">
          <Card
            variant="vertical"
            title="Vertical Card"
            subtitle="This is a subtitle"
            :clickable="true"
            @click="handleCardClick"
          >
            <p>This is the card content. It can contain any information you want to display.</p>
            <template #footer>
              <Button variant="primary" size="small">Action</Button>
            </template>
          </Card>

          <Card
            variant="horizontal"
            title="Horizontal Card"
            subtitle="Side by side layout"
          >
            <p>Horizontal cards are great for list items.</p>
            <template #action>
              <Button variant="tertiary" size="small">→</Button>
            </template>
          </Card>
        </div>
      </section>

      <!-- Form Controls Section -->
      <section class="component-section">
        <h2>Form Controls</h2>
        <div class="component-grid">
          <Checkbox
            v-model="checkboxValue"
            label="I agree to the terms and conditions"
          />
          <Toggle
            v-model="toggleValue"
            label="Enable notifications"
          />
        </div>
      </section>

      <!-- Badges Section -->
      <section class="component-section">
        <h2>Badges</h2>
        <div class="component-grid">
          <Badge content="New" variant="primary" />
          <Badge content="5" variant="error" />
          <Badge content="Success" variant="success" />
          <Badge variant="warning" dot />
          <Badge content="Info" variant="info" size="large" />
        </div>
      </section>

      <!-- Tab Bar Section -->
      <section class="component-section">
        <h2>Tab Bar</h2>
        <div class="component-grid">
          <TabBar
            v-model="selectedTab"
            :tabs="tabs"
            @tab-change="(data) => console.log('Tab changed:', data)"
          />
        </div>
      </section>
    </main>
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background-color: #F2F2F7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  background-color: #FFFFFF;
  padding: 32px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: #1C1C1E;
}

.app-header p {
  margin: 0;
  font-size: 16px;
  color: #8E8E93;
}

.app-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px;
}

.component-section {
  background-color: #FFFFFF;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.component-section h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1C1C1E;
  border-bottom: 2px solid #F2F2F7;
  padding-bottom: 12px;
}

.component-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

.component-grid > * {
  flex: 0 0 auto;
}

/* Responsive */
@media (max-width: 768px) {
  .app-main {
    padding: 16px;
  }

  .component-section {
    padding: 16px;
  }

  .component-grid {
    flex-direction: column;
  }

  .component-grid > * {
    width: 100%;
  }
}
</style>
