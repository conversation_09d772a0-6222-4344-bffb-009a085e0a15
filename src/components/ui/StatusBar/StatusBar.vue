<template>
  <div :class="statusBarClasses">
    <!-- Left side - Time -->
    <div class="status-bar-left">
      <span class="status-bar-time">{{ currentTime }}</span>
    </div>

    <!-- Right side - Battery, WiFi, Signal -->
    <div class="status-bar-right">
      <div class="status-bar-signal">
        <div class="signal-bar" :class="{ active: signalStrength >= 1 }"></div>
        <div class="signal-bar" :class="{ active: signalStrength >= 2 }"></div>
        <div class="signal-bar" :class="{ active: signalStrength >= 3 }"></div>
        <div class="signal-bar" :class="{ active: signalStrength >= 4 }"></div>
      </div>
      
      <div class="status-bar-wifi">
        <svg width="15" height="11" viewBox="0 0 15 11" fill="none">
          <path d="M7.5 11C8.05 11 8.5 10.55 8.5 10C8.5 9.45 8.05 9 7.5 9C6.95 9 6.5 9.45 6.5 10C6.5 10.55 6.95 11 7.5 11Z" fill="currentColor"/>
          <path d="M7.5 7.5C8.85 7.5 10.1 8.05 11 8.95L12.05 7.9C10.8 6.65 9.2 6 7.5 6C5.8 6 4.2 6.65 2.95 7.9L4 8.95C4.9 8.05 6.15 7.5 7.5 7.5Z" fill="currentColor"/>
          <path d="M7.5 4.5C9.65 4.5 11.65 5.35 13.15 6.85L14.2 5.8C12.35 3.95 9.95 3 7.5 3C5.05 3 2.65 3.95 0.8 5.8L1.85 6.85C3.35 5.35 5.35 4.5 7.5 4.5Z" fill="currentColor"/>
          <path d="M7.5 1.5C10.4 1.5 13.15 2.65 15.25 4.75L16.3 3.7C13.85 1.25 10.75 0 7.5 0C4.25 0 1.15 1.25 -1.3 3.7L-0.25 4.75C1.85 2.65 4.6 1.5 7.5 1.5Z" fill="currentColor"/>
        </svg>
      </div>

      <div class="status-bar-battery">
        <div class="battery-outline">
          <div class="battery-fill" :style="{ width: `${batteryLevel}%` }"></div>
        </div>
        <div class="battery-tip"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  theme: {
    type: String,
    default: 'light',
    validator: (value) => ['light', 'dark'].includes(value)
  },
  signalStrength: {
    type: Number,
    default: 4,
    validator: (value) => value >= 0 && value <= 4
  },
  batteryLevel: {
    type: Number,
    default: 85,
    validator: (value) => value >= 0 && value <= 100
  }
})

const currentTime = ref('')
let timeInterval = null

const statusBarClasses = computed(() => [
  'status-bar',
  `status-bar--${props.theme}`
])

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: false 
  })
}

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<script>
import { computed } from 'vue'
</script>

<style scoped>
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  padding: 0 20px;
  font-size: 17px;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.status-bar--light {
  background-color: #FFFFFF;
  color: #000000;
}

.status-bar--dark {
  background-color: #000000;
  color: #FFFFFF;
}

.status-bar-left {
  display: flex;
  align-items: center;
}

.status-bar-time {
  font-weight: 600;
  letter-spacing: -0.3px;
}

.status-bar-right {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-bar-signal {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: 12px;
}

.signal-bar {
  width: 3px;
  background-color: currentColor;
  opacity: 0.3;
  border-radius: 1px;
}

.signal-bar:nth-child(1) { height: 3px; }
.signal-bar:nth-child(2) { height: 6px; }
.signal-bar:nth-child(3) { height: 9px; }
.signal-bar:nth-child(4) { height: 12px; }

.signal-bar.active {
  opacity: 1;
}

.status-bar-wifi {
  display: flex;
  align-items: center;
  width: 15px;
  height: 11px;
}

.status-bar-battery {
  display: flex;
  align-items: center;
  gap: 1px;
}

.battery-outline {
  width: 24px;
  height: 12px;
  border: 1px solid currentColor;
  border-radius: 2px;
  position: relative;
  opacity: 0.4;
}

.battery-fill {
  height: 100%;
  background-color: currentColor;
  border-radius: 1px;
  transition: width 0.3s ease;
}

.battery-tip {
  width: 1px;
  height: 4px;
  background-color: currentColor;
  border-radius: 0 1px 1px 0;
  opacity: 0.4;
}

/* Battery level colors */
.status-bar-battery .battery-fill {
  background-color: #34C759; /* Green */
}

.status-bar--light .battery-fill {
  background-color: #34C759;
}

.status-bar--dark .battery-fill {
  background-color: #30D158;
}

/* Low battery warning */
.status-bar-battery[data-low-battery] .battery-fill {
  background-color: #FF3B30;
}
</style>
