<template>
  <div :class="homeIndicatorClasses">
    <div class="home-indicator-bar"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  theme: {
    type: String,
    default: 'light',
    validator: (value) => ['light', 'dark'].includes(value)
  },
  position: {
    type: String,
    default: 'bottom',
    validator: (value) => ['bottom', 'top'].includes(value)
  }
})

const homeIndicatorClasses = computed(() => [
  'home-indicator',
  `home-indicator--${props.theme}`,
  `home-indicator--${props.position}`
])
</script>

<style scoped>
.home-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 34px;
  padding: 0 20px;
}

.home-indicator--bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

.home-indicator--top {
  position: relative;
}

.home-indicator-bar {
  width: 134px;
  height: 5px;
  border-radius: 2.5px;
  transition: all 0.2s ease;
}

.home-indicator--light .home-indicator-bar {
  background-color: #000000;
  opacity: 0.3;
}

.home-indicator--dark .home-indicator-bar {
  background-color: #FFFFFF;
  opacity: 0.6;
}

/* Hover effect for interactive states */
.home-indicator:hover .home-indicator-bar {
  opacity: 0.8;
  transform: scaleY(1.2);
}

/* Animation for swipe gestures */
@keyframes swipe-up {
  0% {
    transform: translateY(0) scaleY(1);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-2px) scaleY(1.4);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) scaleY(1);
    opacity: 0.3;
  }
}

.home-indicator-bar.animate-swipe {
  animation: swipe-up 0.3s ease-out;
}
</style>
