<template>
  <div :class="listItemClasses" @click="handleClick">
    <!-- Left content -->
    <div v-if="$slots.left || avatar || icon" class="list-item-left">
      <slot name="left">
        <Avatar v-if="avatar" :src="avatar" :name="avatarName" :size="avatarSize" />
        <div v-else-if="icon" class="list-item-icon">
          <component :is="icon" />
        </div>
      </slot>
    </div>

    <!-- Main content -->
    <div class="list-item-content">
      <div class="list-item-main">
        <div class="list-item-title">
          <slot name="title">{{ title }}</slot>
        </div>
        <div v-if="subtitle || $slots.subtitle" class="list-item-subtitle">
          <slot name="subtitle">{{ subtitle }}</slot>
        </div>
      </div>
      <div v-if="description || $slots.description" class="list-item-description">
        <slot name="description">{{ description }}</slot>
      </div>
    </div>

    <!-- Right content -->
    <div v-if="$slots.right || showArrow || badge || toggle !== null" class="list-item-right">
      <slot name="right">
        <Badge v-if="badge" :content="badge" size="small" />
        <Toggle v-if="toggle !== null" :modelValue="toggle" @update:modelValue="handleToggle" />
        <div v-if="showArrow" class="list-item-arrow">
          <ChevronRightIcon class="list-item-arrow-icon" />
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ChevronRightIcon } from '@heroicons/vue/24/solid'
import Avatar from '../Avatar/Avatar.vue'
import Badge from '../Badge/Badge.vue'
import Toggle from '../Toggle/Toggle.vue'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  avatar: {
    type: String,
    default: ''
  },
  avatarName: {
    type: String,
    default: ''
  },
  avatarSize: {
    type: String,
    default: 'medium'
  },
  icon: {
    type: [String, Object],
    default: null
  },
  badge: {
    type: [String, Number],
    default: null
  },
  toggle: {
    type: Boolean,
    default: null
  },
  showArrow: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click', 'toggle'])

const listItemClasses = computed(() => [
  'list-item',
  {
    'list-item--clickable': props.clickable && !props.disabled,
    'list-item--disabled': props.disabled
  }
])

const handleClick = (event) => {
  if (!props.disabled && props.clickable) {
    emit('click', event)
  }
}

const handleToggle = (value) => {
  emit('toggle', value)
}
</script>

<style scoped>
.list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #FFFFFF;
  border-bottom: 1px solid #F2F2F7;
  transition: background-color 0.2s ease;
}

.list-item--clickable {
  cursor: pointer;
}

.list-item--clickable:hover {
  background-color: #F2F2F7;
}

.list-item--clickable:active {
  background-color: #E5E5EA;
}

.list-item--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.list-item-left {
  flex-shrink: 0;
}

.list-item-icon {
  width: 24px;
  height: 24px;
  color: #8E8E93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-main {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.list-item-title {
  font-size: 16px;
  font-weight: 500;
  color: #1C1C1E;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.list-item-subtitle {
  font-size: 14px;
  color: #8E8E93;
  flex-shrink: 0;
}

.list-item-description {
  font-size: 14px;
  color: #8E8E93;
  margin-top: 4px;
  line-height: 1.4;
}

.list-item-right {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.list-item-arrow {
  width: 16px;
  height: 16px;
  color: #C7C7CC;
}

.list-item-arrow-icon {
  width: 100%;
  height: 100%;
}

/* First and last item styling */
.list-item:first-child {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.list-item:last-child {
  border-bottom: none;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

/* Single item styling */
.list-item:only-child {
  border-radius: 12px;
  border-bottom: none;
}
</style>
