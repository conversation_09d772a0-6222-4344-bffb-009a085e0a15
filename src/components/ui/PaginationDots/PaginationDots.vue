<template>
  <div class="pagination-dots">
    <button
      v-for="(dot, index) in totalDots"
      :key="index"
      :class="getDotClasses(index)"
      @click="handleDotClick(index)"
      :disabled="disabled"
    >
      <span class="sr-only">Go to page {{ index + 1 }}</span>
    </button>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  total: {
    type: Number,
    required: true,
    validator: (value) => value > 0
  },
  current: {
    type: Number,
    default: 0,
    validator: (value) => value >= 0
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['change'])

const totalDots = computed(() => props.total)

const getDotClasses = (index) => [
  'pagination-dot',
  `pagination-dot--${props.size}`,
  {
    'pagination-dot--active': index === props.current,
    'pagination-dot--clickable': props.clickable && !props.disabled,
    'pagination-dot--disabled': props.disabled
  }
]

const handleDotClick = (index) => {
  if (!props.disabled && props.clickable && index !== props.current) {
    emit('change', index)
  }
}
</script>

<style scoped>
.pagination-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.pagination-dot {
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s ease;
  background-color: #E5E5EA;
  position: relative;
}

.pagination-dot--small {
  width: 6px;
  height: 6px;
}

.pagination-dot--medium {
  width: 8px;
  height: 8px;
}

.pagination-dot--large {
  width: 10px;
  height: 10px;
}

.pagination-dot--active {
  background-color: #007AFF;
  transform: scale(1.2);
}

.pagination-dot--clickable:hover:not(.pagination-dot--active):not(.pagination-dot--disabled) {
  background-color: #C7C7CC;
  transform: scale(1.1);
}

.pagination-dot--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-dot--disabled:hover {
  transform: none;
}

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Animation for active dot */
.pagination-dot--active::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(0, 122, 255, 0.3);
  transform: translate(-50%, -50%) scale(0);
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  to {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}
</style>
