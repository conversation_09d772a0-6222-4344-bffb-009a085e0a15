<template>
  <button 
    :class="buttonClasses"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot />
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'tertiary'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const buttonClasses = computed(() => [
  'btn',
  `btn--${props.variant}`,
  `btn--${props.size}`,
  {
    'btn--disabled': props.disabled
  }
])
</script>

<style scoped>
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

.btn:focus {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
}

/* Sizes - Mobile Optimized */
.btn--small {
  padding: 10px 20px;
  font-size: 16px;
  min-height: 44px; /* Minimum touch target for mobile */
}

.btn--medium {
  padding: 14px 28px;
  font-size: 18px;
  min-height: 48px;
}

.btn--large {
  padding: 18px 36px;
  font-size: 20px;
  min-height: 56px;
}

/* Primary Button */
.btn--primary {
  background-color: #007AFF;
  color: white;
}

.btn--primary:hover:not(.btn--disabled) {
  background-color: #0056CC;
}

.btn--primary:active:not(.btn--disabled) {
  background-color: #004499;
}

/* Secondary Button */
.btn--secondary {
  background-color: #F2F2F7;
  color: #007AFF;
  border: 1px solid #D1D1D6;
}

.btn--secondary:hover:not(.btn--disabled) {
  background-color: #E5E5EA;
}

.btn--secondary:active:not(.btn--disabled) {
  background-color: #D1D1D6;
}

/* Tertiary Button */
.btn--tertiary {
  background-color: transparent;
  color: #007AFF;
}

.btn--tertiary:hover:not(.btn--disabled) {
  background-color: #F2F2F7;
}

.btn--tertiary:active:not(.btn--disabled) {
  background-color: #E5E5EA;
}

/* Disabled State */
.btn--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
