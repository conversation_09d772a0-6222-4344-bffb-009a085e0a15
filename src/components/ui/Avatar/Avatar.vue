<template>
  <div :class="avatarClasses">
    <img 
      v-if="src" 
      :src="src" 
      :alt="alt"
      class="avatar-image"
      @error="handleImageError"
    />
    <div v-else-if="name" class="avatar-initials">
      {{ initials }}
    </div>
    <div v-else class="avatar-placeholder">
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
      </svg>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: 'Avatar'
  },
  name: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

const imageError = ref(false)

const avatarClasses = computed(() => [
  'avatar',
  `avatar--${props.size}`
])

const initials = computed(() => {
  if (!props.name) return ''
  return props.name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
})

const handleImageError = () => {
  imageError.value = true
}
</script>

<style scoped>
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #E5E5EA;
  color: #8E8E93;
  font-weight: 600;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar--small {
  width: 32px;
  height: 32px;
  font-size: 12px;
}

.avatar--medium {
  width: 48px;
  height: 48px;
  font-size: 16px;
}

.avatar--large {
  width: 64px;
  height: 64px;
  font-size: 20px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-initials {
  color: #1C1C1E;
}

.avatar-placeholder {
  width: 60%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-placeholder svg {
  width: 100%;
  height: 100%;
}
</style>
