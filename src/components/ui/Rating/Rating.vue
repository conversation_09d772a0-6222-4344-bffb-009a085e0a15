<template>
  <div class="rating">
    <div class="rating-stars">
      <button
        v-for="star in maxRating"
        :key="star"
        :class="getStarClasses(star)"
        @click="handleStarClick(star)"
        @mouseenter="handleStarHover(star)"
        @mouseleave="handleMouseLeave"
        :disabled="readonly"
      >
        <svg viewBox="0 0 24 24" class="star-icon">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      </button>
    </div>
    <div v-if="showText" class="rating-text">
      {{ ratingText }}
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  maxRating: {
    type: Number,
    default: 5
  },
  readonly: {
    type: Boolean,
    default: false
  },
  showText: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue'])

const hoverRating = ref(0)

const getStarClasses = (star) => [
  'rating-star',
  `rating-star--${props.size}`,
  {
    'rating-star--filled': star <= (hoverRating.value || props.modelValue),
    'rating-star--readonly': props.readonly
  }
]

const ratingText = computed(() => {
  const rating = hoverRating.value || props.modelValue
  const texts = ['Poor', 'Fair', 'Good', 'Very Good', 'Excellent']
  return texts[Math.ceil(rating) - 1] || 'No rating'
})

const handleStarClick = (star) => {
  if (!props.readonly) {
    emit('update:modelValue', star)
  }
}

const handleStarHover = (star) => {
  if (!props.readonly) {
    hoverRating.value = star
  }
}

const handleMouseLeave = () => {
  hoverRating.value = 0
}
</script>

<style scoped>
.rating {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rating-stars {
  display: flex;
  gap: 4px;
}

.rating-star {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.rating-star:hover:not(.rating-star--readonly) {
  transform: scale(1.1);
}

.rating-star--readonly {
  cursor: default;
}

.star-icon {
  display: block;
  fill: #E5E5EA;
  transition: fill 0.2s ease;
}

.rating-star--small .star-icon {
  width: 16px;
  height: 16px;
}

.rating-star--medium .star-icon {
  width: 20px;
  height: 20px;
}

.rating-star--large .star-icon {
  width: 24px;
  height: 24px;
}

.rating-star--filled .star-icon {
  fill: #FF9500;
}

.rating-text {
  font-size: 14px;
  color: #8E8E93;
  text-align: center;
}

.rating-star--small + .rating-text {
  font-size: 12px;
}

.rating-star--large + .rating-text {
  font-size: 16px;
}
</style>
