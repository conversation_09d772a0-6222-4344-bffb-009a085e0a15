<template>
  <span :class="tagClasses" @click="handleClick">
    <component v-if="icon" :is="icon" class="tag-icon" />
    <slot name="icon"></slot>
    <span class="tag-text">
      <slot>{{ text }}</slot>
    </span>
    <button
      v-if="closable"
      class="tag-close"
      @click.stop="handleClose"
    >
      <XMarkIcon class="tag-close-icon" />
    </button>
  </span>
</template>

<script setup>
import { computed } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/solid'

const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'success', 'warning', 'error', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  closable: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  clickable: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  selected: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  icon: {
    type: [String, Object],
    default: null
  }
})

const emit = defineEmits(['click', 'close'])

const tagClasses = computed(() => [
  'tag',
  `tag--${props.variant}`,
  `tag--${props.size}`,
  {
    'tag--clickable': props.clickable && !props.disabled,
    'tag--selected': props.selected,
    'tag--disabled': props.disabled,
    'tag--with-icon': props.icon,
    'tag--closable': props.closable
  }
])

const handleClick = (event) => {
  if (!props.disabled && props.clickable) {
    emit('click', event)
  }
}

const handleClose = (event) => {
  emit('close', event)
}
</script>

<style scoped>
.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border-radius: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  white-space: nowrap;
}

/* Sizes */
.tag--small {
  padding: 4px 8px;
  font-size: 12px;
  height: 24px;
}

.tag--medium {
  padding: 6px 12px;
  font-size: 14px;
  height: 32px;
}

.tag--large {
  padding: 8px 16px;
  font-size: 16px;
  height: 40px;
}

/* Variants */
.tag--default {
  background-color: #F2F2F7;
  color: #1C1C1E;
}

.tag--primary {
  background-color: #E3F2FD;
  color: #1565C0;
  border-color: #2196F3;
}

.tag--success {
  background-color: #E8F5E8;
  color: #2E7D32;
  border-color: #4CAF50;
}

.tag--warning {
  background-color: #FFF8E1;
  color: #F57C00;
  border-color: #FF9800;
}

.tag--error {
  background-color: #FFEBEE;
  color: #C62828;
  border-color: #F44336;
}

.tag--info {
  background-color: #E0F2F1;
  color: #00695C;
  border-color: #009688;
}

/* States */
.tag--clickable {
  cursor: pointer;
}

.tag--clickable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag--selected {
  background-color: #007AFF;
  color: #FFFFFF;
  border-color: #007AFF;
}

.tag--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tag--disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Icon */
.tag-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.tag--small .tag-icon {
  width: 12px;
  height: 12px;
}

.tag--large .tag-icon {
  width: 16px;
  height: 16px;
}

/* Close button */
.tag-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  margin-left: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.tag-close:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.1);
}

.tag-close-icon {
  width: 12px;
  height: 12px;
}

.tag--small .tag-close-icon {
  width: 10px;
  height: 10px;
}

.tag--large .tag-close-icon {
  width: 14px;
  height: 14px;
}

.tag-text {
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
