<template>
  <div :class="spinnerClasses">
    <div class="spinner-circle"></div>
    <div v-if="text" class="spinner-text">{{ text }}</div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  text: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: '#007AFF'
  }
})

const spinnerClasses = computed(() => [
  'loading-spinner',
  `loading-spinner--${props.size}`
])
</script>

<style scoped>
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.spinner-circle {
  border-radius: 50%;
  border: 3px solid #E5E5EA;
  border-top-color: v-bind(color);
  animation: spin 1s linear infinite;
}

.loading-spinner--small .spinner-circle {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner--medium .spinner-circle {
  width: 32px;
  height: 32px;
  border-width: 3px;
}

.loading-spinner--large .spinner-circle {
  width: 48px;
  height: 48px;
  border-width: 4px;
}

.spinner-text {
  font-size: 14px;
  color: #8E8E93;
  text-align: center;
}

.loading-spinner--small .spinner-text {
  font-size: 12px;
}

.loading-spinner--large .spinner-text {
  font-size: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
