<template>
  <label :class="toggleClasses">
    <input
      type="checkbox"
      :checked="modelValue"
      :disabled="disabled"
      class="toggle-input"
      @change="$emit('update:modelValue', $event.target.checked)"
    />
    <div class="toggle-track">
      <div class="toggle-thumb"></div>
    </div>
    <span v-if="label || $slots.default" class="toggle-label">
      <slot>{{ label }}</slot>
    </span>
  </label>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const toggleClasses = computed(() => [
  'toggle',
  {
    'toggle--disabled': props.disabled,
    'toggle--checked': props.modelValue
  }
])
</script>

<style scoped>
.toggle {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  user-select: none;
}

.toggle--disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.toggle-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.toggle-track {
  position: relative;
  width: 48px;
  height: 28px;
  background-color: #E5E5EA;
  border-radius: 14px;
  transition: all 0.2s ease;
}

.toggle--checked .toggle-track {
  background-color: #34C759;
}

.toggle-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 24px;
  height: 24px;
  background-color: #FFFFFF;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.toggle--checked .toggle-thumb {
  transform: translateX(20px);
}

.toggle:hover:not(.toggle--disabled) .toggle-track {
  background-color: #D1D1D6;
}

.toggle--checked:hover:not(.toggle--disabled) .toggle-track {
  background-color: #30B050;
}

.toggle-label {
  font-size: 16px;
  color: #1C1C1E;
}

.toggle--disabled .toggle-label {
  color: #8E8E93;
}
</style>
