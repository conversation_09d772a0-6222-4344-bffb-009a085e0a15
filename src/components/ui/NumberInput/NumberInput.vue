<template>
  <div class="number-input">
    <label v-if="label" class="number-input-label">
      {{ label }}
    </label>
    <div :class="inputClasses">
      <button 
        class="number-input-button number-input-button--decrease"
        @click="decrease"
        :disabled="disabled || (min !== null && modelValue <= min)"
      >
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 13H5v-2h14v2z"/>
        </svg>
      </button>
      
      <input
        ref="inputRef"
        type="number"
        :value="modelValue"
        :min="min"
        :max="max"
        :step="step"
        :disabled="disabled"
        :placeholder="placeholder"
        class="number-input-field"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <button 
        class="number-input-button number-input-button--increase"
        @click="increase"
        :disabled="disabled || (max !== null && modelValue >= max)"
      >
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
        </svg>
      </button>
    </div>
    <div v-if="error" class="number-input-error">
      {{ error }}
    </div>
    <div v-else-if="helper" class="number-input-helper">
      {{ helper }}
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  min: {
    type: Number,
    default: null
  },
  max: {
    type: Number,
    default: null
  },
  step: {
    type: Number,
    default: 1
  },
  disabled: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  helper: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur'])

const inputRef = ref(null)
const isFocused = ref(false)

const inputClasses = computed(() => [
  'number-input-wrapper',
  `number-input-wrapper--${props.size}`,
  {
    'number-input-wrapper--focused': isFocused.value,
    'number-input-wrapper--error': props.error,
    'number-input-wrapper--disabled': props.disabled
  }
])

const clampValue = (value) => {
  let clampedValue = value
  if (props.min !== null) clampedValue = Math.max(props.min, clampedValue)
  if (props.max !== null) clampedValue = Math.min(props.max, clampedValue)
  return clampedValue
}

const handleInput = (event) => {
  const value = parseFloat(event.target.value)
  if (!isNaN(value)) {
    const clampedValue = clampValue(value)
    emit('update:modelValue', clampedValue)
  }
}

const handleFocus = (event) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  isFocused.value = false
  // Ensure value is clamped on blur
  const clampedValue = clampValue(props.modelValue)
  if (clampedValue !== props.modelValue) {
    emit('update:modelValue', clampedValue)
  }
  emit('blur', event)
}

const increase = () => {
  if (props.disabled) return
  const newValue = props.modelValue + props.step
  const clampedValue = clampValue(newValue)
  emit('update:modelValue', clampedValue)
}

const decrease = () => {
  if (props.disabled) return
  const newValue = props.modelValue - props.step
  const clampedValue = clampValue(newValue)
  emit('update:modelValue', clampedValue)
}
</script>

<style scoped>
.number-input {
  width: 100%;
}

.number-input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1C1C1E;
  margin-bottom: 8px;
}

.number-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #FFFFFF;
  border: 2px solid #E5E5EA;
  border-radius: 12px;
  transition: all 0.2s ease;
  overflow: hidden;
}

.number-input-wrapper--small {
  height: 36px;
}

.number-input-wrapper--medium {
  height: 44px;
}

.number-input-wrapper--large {
  height: 52px;
}

.number-input-wrapper--focused {
  border-color: #007AFF;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.number-input-wrapper--error {
  border-color: #FF3B30;
}

.number-input-wrapper--disabled {
  background-color: #F2F2F7;
  opacity: 0.6;
}

.number-input-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #007AFF;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.number-input-wrapper--small .number-input-button {
  width: 36px;
  height: 36px;
}

.number-input-wrapper--medium .number-input-button {
  width: 44px;
  height: 44px;
}

.number-input-wrapper--large .number-input-button {
  width: 52px;
  height: 52px;
}

.number-input-button:hover:not(:disabled) {
  background-color: #F2F2F7;
}

.number-input-button:disabled {
  color: #C7C7CC;
  cursor: not-allowed;
}

.number-input-button svg {
  width: 16px;
  height: 16px;
}

.number-input-field {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 16px;
  color: #1C1C1E;
  text-align: center;
  padding: 0 8px;
}

.number-input-wrapper--small .number-input-field {
  font-size: 14px;
}

.number-input-wrapper--large .number-input-field {
  font-size: 18px;
}

.number-input-field::placeholder {
  color: #8E8E93;
}

.number-input-field:disabled {
  color: #8E8E93;
}

/* Remove default number input arrows */
.number-input-field::-webkit-outer-spin-button,
.number-input-field::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.number-input-field[type=number] {
  -moz-appearance: textfield;
}

.number-input-error {
  font-size: 12px;
  color: #FF3B30;
  margin-top: 4px;
}

.number-input-helper {
  font-size: 12px;
  color: #8E8E93;
  margin-top: 4px;
}
</style>
