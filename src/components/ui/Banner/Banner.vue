<template>
  <div :class="bannerClasses" v-if="visible">
    <div class="banner-icon" v-if="showIcon">
      <component :is="iconComponent" class="banner-icon-svg" />
    </div>
    <div class="banner-content">
      <div class="banner-title" v-if="title">{{ title }}</div>
      <div class="banner-message">
        <slot>{{ message }}</slot>
      </div>
    </div>
    <button 
      v-if="closable" 
      class="banner-close"
      @click="handleClose"
    >
      <XMarkIcon class="banner-close-icon" />
    </button>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import {
  InformationCircleIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/solid'

const props = defineProps({
  variant: {
    type: String,
    default: 'info',
    validator: (value) => ['info', 'success', 'warning', 'error'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  closable: {
    type: Boolean,
    default: true
  },
  showIcon: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['close'])

const visible = ref(true)

const bannerClasses = computed(() => [
  'banner',
  `banner--${props.variant}`
])

const iconComponent = computed(() => {
  const icons = {
    info: InformationCircleIcon,
    success: CheckCircleIcon,
    warning: ExclamationTriangleIcon,
    error: XCircleIcon
  }
  return icons[props.variant]
})

const handleClose = () => {
  visible.value = false
  emit('close')
}
</script>

<style scoped>
.banner {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid;
  animation: bannerSlideIn 0.3s ease-out;
}

.banner--info {
  background-color: #E3F2FD;
  border-left-color: #2196F3;
  color: #1565C0;
}

.banner--success {
  background-color: #E8F5E8;
  border-left-color: #34C759;
  color: #2E7D32;
}

.banner--warning {
  background-color: #FFF8E1;
  border-left-color: #FF9500;
  color: #F57C00;
}

.banner--error {
  background-color: #FFEBEE;
  border-left-color: #FF3B30;
  color: #C62828;
}

.banner-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.banner-content {
  flex: 1;
}

.banner-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.banner-message {
  font-size: 14px;
  line-height: 1.4;
}

.banner-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.banner-close:hover {
  opacity: 1;
}

.banner-close-icon {
  width: 16px;
  height: 16px;
}

.banner-icon-svg {
  width: 100%;
  height: 100%;
}

@keyframes bannerSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
