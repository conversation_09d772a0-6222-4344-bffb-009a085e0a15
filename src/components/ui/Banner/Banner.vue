<template>
  <div :class="bannerClasses" v-if="visible">
    <div class="banner-icon" v-if="showIcon">
      <component :is="iconComponent" />
    </div>
    <div class="banner-content">
      <div class="banner-title" v-if="title">{{ title }}</div>
      <div class="banner-message">
        <slot>{{ message }}</slot>
      </div>
    </div>
    <button 
      v-if="closable" 
      class="banner-close"
      @click="handleClose"
    >
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
      </svg>
    </button>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  variant: {
    type: String,
    default: 'info',
    validator: (value) => ['info', 'success', 'warning', 'error'].includes(value)
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  closable: {
    type: Boolean,
    default: true
  },
  showIcon: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['close'])

const visible = ref(true)

const bannerClasses = computed(() => [
  'banner',
  `banner--${props.variant}`
])

const iconComponent = computed(() => {
  const icons = {
    info: 'InfoIcon',
    success: 'SuccessIcon', 
    warning: 'WarningIcon',
    error: 'ErrorIcon'
  }
  return icons[props.variant]
})

const handleClose = () => {
  visible.value = false
  emit('close')
}
</script>

<style scoped>
.banner {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border-left: 4px solid;
  animation: bannerSlideIn 0.3s ease-out;
}

.banner--info {
  background-color: #E3F2FD;
  border-left-color: #2196F3;
  color: #1565C0;
}

.banner--success {
  background-color: #E8F5E8;
  border-left-color: #34C759;
  color: #2E7D32;
}

.banner--warning {
  background-color: #FFF8E1;
  border-left-color: #FF9500;
  color: #F57C00;
}

.banner--error {
  background-color: #FFEBEE;
  border-left-color: #FF3B30;
  color: #C62828;
}

.banner-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.banner-content {
  flex: 1;
}

.banner-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
}

.banner-message {
  font-size: 14px;
  line-height: 1.4;
}

.banner-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: inherit;
  opacity: 0.7;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.banner-close:hover {
  opacity: 1;
}

.banner-close svg {
  width: 16px;
  height: 16px;
}

@keyframes bannerSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Icon components */
.banner-icon svg {
  width: 100%;
  height: 100%;
  fill: currentColor;
}
</style>
