<template>
  <div :class="bubbleClasses">
    <div class="message-content">
      <slot>{{ message }}</slot>
    </div>
    <div v-if="showTime" class="message-time">
      {{ time }}
    </div>
    <div v-if="showTip" class="message-tip"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  message: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'sent',
    validator: (value) => ['sent', 'received'].includes(value)
  },
  showTip: {
    type: Boolean,
    default: true
  },
  showTime: {
    type: Boolean,
    default: false
  },
  time: {
    type: String,
    default: ''
  }
})

const bubbleClasses = computed(() => [
  'message-bubble',
  `message-bubble--${props.type}`,
  {
    'message-bubble--with-tip': props.showTip
  }
])
</script>

<style scoped>
.message-bubble {
  position: relative;
  max-width: 280px;
  margin-bottom: 8px;
  word-wrap: break-word;
}

.message-bubble--sent {
  margin-left: auto;
  margin-right: 16px;
}

.message-bubble--received {
  margin-right: auto;
  margin-left: 16px;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 16px;
  line-height: 1.4;
}

.message-bubble--sent .message-content {
  background-color: #007AFF;
  color: #FFFFFF;
  border-bottom-right-radius: 4px;
}

.message-bubble--received .message-content {
  background-color: #E5E5EA;
  color: #1C1C1E;
  border-bottom-left-radius: 4px;
}

.message-time {
  font-size: 12px;
  color: #8E8E93;
  margin-top: 4px;
  text-align: center;
}

.message-tip {
  position: absolute;
  bottom: 0;
  width: 0;
  height: 0;
}

.message-bubble--sent.message-bubble--with-tip .message-tip {
  right: 0;
  border-left: 8px solid #007AFF;
  border-bottom: 8px solid transparent;
}

.message-bubble--received.message-bubble--with-tip .message-tip {
  left: 0;
  border-right: 8px solid #E5E5EA;
  border-bottom: 8px solid transparent;
}

/* Animation */
.message-bubble {
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
