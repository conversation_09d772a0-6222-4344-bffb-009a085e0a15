<template>
  <div :class="cardClasses" @click="handleClick">
    <!-- Image Section -->
    <div v-if="image || $slots.image" class="card-image">
      <slot name="image">
        <img :src="image" :alt="imageAlt" />
      </slot>
    </div>

    <!-- Content Section -->
    <div class="card-content">
      <!-- Header -->
      <div v-if="title || subtitle || $slots.header" class="card-header">
        <slot name="header">
          <div class="card-titles">
            <h3 v-if="title" class="card-title">{{ title }}</h3>
            <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
          </div>
        </slot>
      </div>

      <!-- Body -->
      <div v-if="$slots.default" class="card-body">
        <slot />
      </div>

      <!-- Footer -->
      <div v-if="$slots.footer" class="card-footer">
        <slot name="footer" />
      </div>
    </div>

    <!-- Action Button -->
    <div v-if="$slots.action" class="card-action">
      <slot name="action" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  variant: {
    type: String,
    default: 'vertical',
    validator: (value) => ['vertical', 'horizontal'].includes(value)
  },
  image: {
    type: String,
    default: ''
  },
  imageAlt: {
    type: String,
    default: 'Card image'
  },
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  clickable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const cardClasses = computed(() => [
  'card',
  `card--${props.variant}`,
  {
    'card--clickable': props.clickable
  }
])

const handleClick = (event) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style scoped>
.card {
  background-color: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card--clickable {
  cursor: pointer;
}

.card--clickable:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Vertical Card */
.card--vertical {
  display: flex;
  flex-direction: column;
  max-width: 300px;
}

.card--vertical .card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.card--vertical .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card--vertical .card-content {
  padding: 16px;
  flex: 1;
}

/* Horizontal Card */
.card--horizontal {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.card--horizontal .card-image {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  overflow: hidden;
}

.card--horizontal .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card--horizontal .card-content {
  padding: 16px;
  flex: 1;
}

.card--horizontal .card-action {
  padding: 16px;
  flex-shrink: 0;
}

/* Content Styles */
.card-header {
  margin-bottom: 12px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0 0 4px 0;
}

.card-subtitle {
  font-size: 14px;
  color: #8E8E93;
  margin: 0;
}

.card-body {
  font-size: 14px;
  color: #1C1C1E;
  line-height: 1.5;
}

.card-footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #F2F2F7;
}
</style>
