<template>
  <Teleport to="body">
    <div v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
      <div class="dialog" @click.stop>
        <div v-if="title || $slots.header" class="dialog-header">
          <slot name="header">
            <h3 class="dialog-title">{{ title }}</h3>
          </slot>
        </div>
        
        <div class="dialog-body">
          <slot>{{ message }}</slot>
        </div>
        
        <div v-if="$slots.footer || showDefaultActions" class="dialog-footer">
          <slot name="footer">
            <div class="dialog-actions">
              <Button 
                v-if="showCancel"
                variant="secondary" 
                @click="handleCancel"
              >
                {{ cancelText }}
              </Button>
              <Button 
                variant="primary" 
                @click="handleConfirm"
              >
                {{ confirmText }}
              </Button>
            </div>
          </slot>
        </div>
        
        <button 
          v-if="closable"
          class="dialog-close"
          @click="handleClose"
        >
          <XMarkIcon class="dialog-close-icon" />
        </button>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/solid'
import Button from '../Button/Button.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  closable: {
    type: Boolean,
    default: true
  },
  closeOnOverlay: {
    type: Boolean,
    default: true
  },
  showDefaultActions: {
    type: Boolean,
    default: true
  },
  showCancel: {
    type: Boolean,
    default: true
  },
  confirmText: {
    type: String,
    default: 'Confirm'
  },
  cancelText: {
    type: String,
    default: 'Cancel'
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel', 'close'])

const visible = ref(props.modelValue)

watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
})

watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
  if (newValue) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
})

const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose()
  }
}

const handleClose = () => {
  visible.value = false
  emit('close')
}

const handleConfirm = () => {
  emit('confirm')
  visible.value = false
}

const handleCancel = () => {
  emit('cancel')
  visible.value = false
}

const handleEscape = (event) => {
  if (event.key === 'Escape' && visible.value && props.closable) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleEscape)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscape)
  document.body.style.overflow = ''
})
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
  animation: overlayFadeIn 0.2s ease-out;
}

.dialog {
  background-color: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  animation: dialogSlideIn 0.3s ease-out;
}

.dialog-header {
  padding: 24px 24px 0 24px;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
}

.dialog-body {
  padding: 16px 24px;
  color: #1C1C1E;
  line-height: 1.5;
  overflow-y: auto;
}

.dialog-footer {
  padding: 0 24px 24px 24px;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.dialog-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: #8E8E93;
  transition: all 0.2s ease;
}

.dialog-close:hover {
  background-color: #F2F2F7;
  color: #1C1C1E;
}

.dialog-close-icon {
  width: 16px;
  height: 16px;
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Mobile responsive */
@media (max-width: 480px) {
  .dialog {
    margin: 0;
    border-radius: 16px 16px 0 0;
    max-height: 90vh;
  }
  
  .dialog-actions {
    flex-direction: column-reverse;
  }
}
</style>
