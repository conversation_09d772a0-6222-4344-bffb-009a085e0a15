<template>
  <div class="tab-bar">
    <button
      v-for="(tab, index) in tabs"
      :key="tab.id || index"
      :class="getTabClasses(tab, index)"
      @click="selectTab(tab, index)"
    >
      <div v-if="tab.icon" class="tab-icon">
        <component :is="tab.icon" />
      </div>
      <span class="tab-label">{{ tab.label }}</span>
      <div v-if="tab.badge" class="tab-badge">
        <Badge :content="tab.badge" size="small" variant="error" />
      </div>
    </button>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Badge from '../Badge/Badge.vue'

const props = defineProps({
  tabs: {
    type: Array,
    required: true,
    validator: (tabs) => tabs.every(tab => tab.label)
  },
  modelValue: {
    type: [String, Number],
    default: 0
  }
})

const emit = defineEmits(['update:modelValue', 'tab-change'])

const getTabClasses = (tab, index) => [
  'tab',
  {
    'tab--selected': isSelected(tab, index),
    'tab--disabled': tab.disabled
  }
]

const isSelected = (tab, index) => {
  return props.modelValue === (tab.id || index)
}

const selectTab = (tab, index) => {
  if (tab.disabled) return
  
  const tabId = tab.id || index
  emit('update:modelValue', tabId)
  emit('tab-change', { tab, index, id: tabId })
}
</script>

<style scoped>
.tab-bar {
  display: flex;
  background-color: #F2F2F7;
  border-radius: 12px;
  padding: 4px;
  gap: 2px;
}

.tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #8E8E93;
  position: relative;
}

.tab:hover:not(.tab--disabled) {
  background-color: rgba(0, 122, 255, 0.1);
}

.tab--selected {
  background-color: #FFFFFF;
  color: #007AFF;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab--disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tab-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-label {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.tab-badge {
  position: absolute;
  top: 4px;
  right: 4px;
}

/* Responsive */
@media (max-width: 480px) {
  .tab {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .tab-icon {
    width: 20px;
    height: 20px;
  }
}
</style>
