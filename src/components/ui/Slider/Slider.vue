<template>
  <div class="slider">
    <div v-if="label" class="slider-label">
      <span>{{ label }}</span>
      <span v-if="showValue" class="slider-value">{{ displayValue }}</span>
    </div>
    <div 
      class="slider-track"
      @click="handleTrackClick"
      ref="trackRef"
    >
      <div 
        class="slider-fill" 
        :style="{ width: `${percentage}%` }"
      ></div>
      <div 
        class="slider-thumb"
        :style="{ left: `${percentage}%` }"
        @mousedown="handleMouseDown"
        @touchstart="handleTouchStart"
      ></div>
    </div>
    <div v-if="showMinMax" class="slider-range">
      <span class="slider-min">{{ min }}</span>
      <span class="slider-max">{{ max }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: 100
  },
  step: {
    type: Number,
    default: 1
  },
  label: {
    type: String,
    default: ''
  },
  showValue: {
    type: Boolean,
    default: false
  },
  showMinMax: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const trackRef = ref(null)
const isDragging = ref(false)

const percentage = computed(() => {
  return ((props.modelValue - props.min) / (props.max - props.min)) * 100
})

const displayValue = computed(() => {
  return Math.round(props.modelValue * 100) / 100
})

const updateValue = (clientX) => {
  if (!trackRef.value || props.disabled) return
  
  const rect = trackRef.value.getBoundingClientRect()
  const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
  const rawValue = props.min + percentage * (props.max - props.min)
  const steppedValue = Math.round(rawValue / props.step) * props.step
  const clampedValue = Math.max(props.min, Math.min(props.max, steppedValue))
  
  emit('update:modelValue', clampedValue)
}

const handleTrackClick = (event) => {
  updateValue(event.clientX)
}

const handleMouseDown = (event) => {
  if (props.disabled) return
  isDragging.value = true
  event.preventDefault()
}

const handleTouchStart = (event) => {
  if (props.disabled) return
  isDragging.value = true
  event.preventDefault()
}

const handleMouseMove = (event) => {
  if (isDragging.value) {
    updateValue(event.clientX)
  }
}

const handleTouchMove = (event) => {
  if (isDragging.value) {
    updateValue(event.touches[0].clientX)
  }
}

const handleEnd = () => {
  isDragging.value = false
}

onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleEnd)
  document.addEventListener('touchmove', handleTouchMove)
  document.addEventListener('touchend', handleEnd)
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleEnd)
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleEnd)
})
</script>

<style scoped>
.slider {
  width: 100%;
  user-select: none;
}

.slider-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #1C1C1E;
}

.slider-value {
  font-weight: 600;
  color: #007AFF;
}

.slider-track {
  position: relative;
  height: 6px;
  background-color: #E5E5EA;
  border-radius: 3px;
  cursor: pointer;
  margin: 12px 0;
}

.slider-fill {
  height: 100%;
  background-color: #007AFF;
  border-radius: 3px;
  transition: width 0.1s ease;
}

.slider-thumb {
  position: absolute;
  top: 50%;
  width: 24px;
  height: 24px;
  background-color: #FFFFFF;
  border: 2px solid #007AFF;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: grab;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.1s ease;
}

.slider-thumb:hover {
  transform: translate(-50%, -50%) scale(1.1);
}

.slider-thumb:active {
  cursor: grabbing;
  transform: translate(-50%, -50%) scale(1.2);
}

.slider-range {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #8E8E93;
}

/* Disabled state */
.slider:has([disabled]) .slider-track {
  opacity: 0.5;
  cursor: not-allowed;
}

.slider:has([disabled]) .slider-thumb {
  cursor: not-allowed;
}
</style>
