<template>
  <div class="input-wrapper">
    <label v-if="label" :for="inputId" class="input-label">
      {{ label }}
    </label>
    <div class="input-container" :class="inputContainerClasses">
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :class="inputClasses"
        @input="$emit('update:modelValue', $event.target.value)"
        @focus="$emit('focus', $event)"
        @blur="$emit('blur', $event)"
      />
      <div v-if="$slots.suffix" class="input-suffix">
        <slot name="suffix" />
      </div>
    </div>
    <div v-if="error" class="input-error">
      {{ error }}
    </div>
    <div v-else-if="helper" class="input-helper">
      {{ helper }}
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  error: {
    type: String,
    default: ''
  },
  helper: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  state: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'focus', 'error', 'filled', 'inactive'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur'])

const inputId = ref(`input-${Math.random().toString(36).substr(2, 9)}`)

const inputContainerClasses = computed(() => [
  'input-container',
  `input-container--${props.state}`,
  {
    'input-container--disabled': props.disabled,
    'input-container--error': props.error,
    'input-container--filled': props.modelValue
  }
])

const inputClasses = computed(() => [
  'input',
  {
    'input--disabled': props.disabled
  }
])
</script>

<style scoped>
.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  font-weight: 600;
  color: #1C1C1E;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #D1D1D6;
  border-radius: 8px;
  background-color: #FFFFFF;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  border-color: #007AFF;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.input-container--error {
  border-color: #FF3B30;
}

.input-container--error:focus-within {
  border-color: #FF3B30;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1);
}

.input-container--disabled {
  background-color: #F2F2F7;
  border-color: #E5E5EA;
}

.input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #1C1C1E;
  outline: none;
}

.input::placeholder {
  color: #8E8E93;
}

.input--disabled {
  color: #8E8E93;
  cursor: not-allowed;
}

.input-suffix {
  padding-right: 16px;
  display: flex;
  align-items: center;
  color: #8E8E93;
}

.input-error {
  font-size: 12px;
  color: #FF3B30;
}

.input-helper {
  font-size: 12px;
  color: #8E8E93;
}
</style>
