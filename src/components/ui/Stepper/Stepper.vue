<template>
  <div class="stepper">
    <div class="stepper-steps">
      <div
        v-for="(step, index) in steps"
        :key="index"
        :class="getStepClasses(index)"
      >
        <div class="step-indicator">
          <div v-if="getStepState(index) === 'completed'" class="step-check">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
            </svg>
          </div>
          <span v-else class="step-number">{{ index + 1 }}</span>
        </div>
        <div class="step-content">
          <div class="step-title">{{ step.title }}</div>
          <div v-if="step.description" class="step-description">
            {{ step.description }}
          </div>
        </div>
        <div 
          v-if="index < steps.length - 1" 
          :class="getConnectorClasses(index)"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  steps: {
    type: Array,
    required: true,
    validator: (steps) => steps.every(step => step.title)
  },
  currentStep: {
    type: Number,
    default: 0
  },
  orientation: {
    type: String,
    default: 'vertical',
    validator: (value) => ['vertical', 'horizontal'].includes(value)
  }
})

const getStepState = (index) => {
  if (index < props.currentStep) return 'completed'
  if (index === props.currentStep) return 'current'
  return 'pending'
}

const getStepClasses = (index) => [
  'stepper-step',
  `stepper-step--${getStepState(index)}`,
  `stepper-step--${props.orientation}`
]

const getConnectorClasses = (index) => [
  'step-connector',
  `step-connector--${props.orientation}`,
  {
    'step-connector--completed': index < props.currentStep
  }
]
</script>

<style scoped>
.stepper {
  width: 100%;
}

.stepper-steps {
  display: flex;
}

.stepper-step--vertical {
  flex-direction: column;
}

.stepper-step--horizontal {
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.stepper-steps:has(.stepper-step--vertical) {
  flex-direction: column;
}

.stepper-steps:has(.stepper-step--horizontal) {
  flex-direction: row;
  align-items: flex-start;
}

.stepper-step {
  position: relative;
  display: flex;
  gap: 12px;
}

.stepper-step--vertical {
  padding-bottom: 24px;
}

.stepper-step--horizontal {
  padding-right: 24px;
  text-align: center;
}

.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.stepper-step--pending .step-indicator {
  background-color: #E5E5EA;
  color: #8E8E93;
}

.stepper-step--current .step-indicator {
  background-color: #007AFF;
  color: #FFFFFF;
}

.stepper-step--completed .step-indicator {
  background-color: #34C759;
  color: #FFFFFF;
}

.step-check {
  width: 16px;
  height: 16px;
}

.step-content {
  flex: 1;
}

.stepper-step--horizontal .step-content {
  margin-top: 8px;
}

.step-title {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  margin-bottom: 4px;
}

.stepper-step--pending .step-title {
  color: #8E8E93;
}

.step-description {
  font-size: 14px;
  color: #8E8E93;
  line-height: 1.4;
}

.step-connector {
  position: absolute;
  background-color: #E5E5EA;
  transition: background-color 0.2s ease;
}

.step-connector--completed {
  background-color: #34C759;
}

.step-connector--vertical {
  left: 15px;
  top: 32px;
  width: 2px;
  height: calc(100% - 8px);
}

.step-connector--horizontal {
  top: 15px;
  right: -12px;
  width: 24px;
  height: 2px;
}

/* Last step shouldn't have connector */
.stepper-step:last-child .step-connector {
  display: none;
}
</style>
