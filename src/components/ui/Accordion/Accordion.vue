<template>
  <div class="accordion">
    <div
      v-for="(item, index) in items"
      :key="index"
      class="accordion-item"
    >
      <button
        :class="getHeaderClasses(index)"
        @click="toggle(index)"
      >
        <span class="accordion-title">{{ item.title }}</span>
        <div class="accordion-icon">
          <ChevronRightIcon class="accordion-icon-svg" />
        </div>
      </button>
      <div
        :class="getContentClasses(index)"
        :style="getContentStyle(index)"
      >
        <div class="accordion-content-inner">
          <slot :name="`content-${index}`" :item="item">
            {{ item.content }}
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ChevronRightIcon } from '@heroicons/vue/24/solid'

const props = defineProps({
  items: {
    type: Array,
    required: true,
    validator: (items) => items.every(item => item.title)
  },
  multiple: {
    type: Boolean,
    default: false
  },
  defaultOpen: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['toggle'])

const openItems = ref(new Set(props.defaultOpen))

const isOpen = (index) => openItems.value.has(index)

const getHeaderClasses = (index) => [
  'accordion-header',
  {
    'accordion-header--open': isOpen(index)
  }
]

const getContentClasses = (index) => [
  'accordion-content',
  {
    'accordion-content--open': isOpen(index)
  }
]

const getContentStyle = (index) => {
  return isOpen(index) ? {} : { maxHeight: '0px' }
}

const toggle = (index) => {
  if (props.multiple) {
    if (openItems.value.has(index)) {
      openItems.value.delete(index)
    } else {
      openItems.value.add(index)
    }
  } else {
    if (openItems.value.has(index)) {
      openItems.value.clear()
    } else {
      openItems.value.clear()
      openItems.value.add(index)
    }
  }
  
  emit('toggle', {
    index,
    isOpen: openItems.value.has(index),
    openItems: Array.from(openItems.value)
  })
}
</script>

<style scoped>
.accordion {
  border-radius: 12px;
  overflow: hidden;
  background-color: #FFFFFF;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.accordion-item {
  border-bottom: 1px solid #F2F2F7;
}

.accordion-item:last-child {
  border-bottom: none;
}

.accordion-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: left;
}

.accordion-header:hover {
  background-color: #F2F2F7;
}

.accordion-header--open {
  background-color: #F2F2F7;
}

.accordion-title {
  font-size: 16px;
  font-weight: 500;
  color: #1C1C1E;
}

.accordion-icon {
  width: 16px;
  height: 16px;
  color: #8E8E93;
  transition: transform 0.2s ease;
}

.accordion-header--open .accordion-icon {
  transform: rotate(90deg);
}

.accordion-icon-svg {
  width: 100%;
  height: 100%;
}

.accordion-content {
  overflow: hidden;
  transition: max-height 0.3s ease;
  max-height: 0;
}

.accordion-content--open {
  max-height: 500px; /* Adjust based on content */
}

.accordion-content-inner {
  padding: 0 20px 16px 20px;
  color: #8E8E93;
  line-height: 1.5;
}
</style>
