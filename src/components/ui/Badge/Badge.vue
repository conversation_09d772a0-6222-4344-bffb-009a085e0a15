<template>
  <span :class="badgeClasses">
    <slot>{{ content }}</slot>
  </span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  content: {
    type: [String, Number],
    default: ''
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error', 'info'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  dot: {
    type: Boolean,
    default: false
  }
})

const badgeClasses = computed(() => [
  'badge',
  `badge--${props.variant}`,
  `badge--${props.size}`,
  {
    'badge--dot': props.dot
  }
])
</script>

<style scoped>
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  border-radius: 12px;
  white-space: nowrap;
}

/* Sizes */
.badge--small {
  padding: 2px 6px;
  font-size: 10px;
  min-height: 16px;
}

.badge--medium {
  padding: 4px 8px;
  font-size: 12px;
  min-height: 20px;
}

.badge--large {
  padding: 6px 12px;
  font-size: 14px;
  min-height: 24px;
}

/* Dot variant */
.badge--dot {
  width: 8px;
  height: 8px;
  min-height: 8px;
  padding: 0;
  border-radius: 50%;
}

.badge--dot.badge--medium {
  width: 10px;
  height: 10px;
  min-height: 10px;
}

.badge--dot.badge--large {
  width: 12px;
  height: 12px;
  min-height: 12px;
}

/* Variants */
.badge--primary {
  background-color: #007AFF;
  color: #FFFFFF;
}

.badge--secondary {
  background-color: #8E8E93;
  color: #FFFFFF;
}

.badge--success {
  background-color: #34C759;
  color: #FFFFFF;
}

.badge--warning {
  background-color: #FF9500;
  color: #FFFFFF;
}

.badge--error {
  background-color: #FF3B30;
  color: #FFFFFF;
}

.badge--info {
  background-color: #5AC8FA;
  color: #FFFFFF;
}
</style>
