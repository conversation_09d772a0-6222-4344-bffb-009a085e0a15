<template>
  <label :class="checkboxClasses">
    <input
      type="checkbox"
      :checked="modelValue"
      :disabled="disabled"
      class="checkbox-input"
      @change="$emit('update:modelValue', $event.target.checked)"
    />
    <div class="checkbox-box">
      <svg v-if="modelValue" class="checkbox-icon" viewBox="0 0 24 24" fill="currentColor">
        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
      </svg>
    </div>
    <span v-if="label || $slots.default" class="checkbox-label">
      <slot>{{ label }}</slot>
    </span>
  </label>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue'])

const checkboxClasses = computed(() => [
  'checkbox',
  `checkbox--${props.size}`,
  {
    'checkbox--disabled': props.disabled,
    'checkbox--checked': props.modelValue
  }
])
</script>

<style scoped>
.checkbox {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
}

.checkbox--disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.checkbox-box {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #D1D1D6;
  border-radius: 4px;
  background-color: #FFFFFF;
  transition: all 0.2s ease;
  color: #FFFFFF;
}

.checkbox--small .checkbox-box {
  width: 16px;
  height: 16px;
}

.checkbox--medium .checkbox-box {
  width: 20px;
  height: 20px;
}

.checkbox--large .checkbox-box {
  width: 24px;
  height: 24px;
}

.checkbox--checked .checkbox-box {
  background-color: #007AFF;
  border-color: #007AFF;
}

.checkbox:hover:not(.checkbox--disabled) .checkbox-box {
  border-color: #007AFF;
}

.checkbox-icon {
  width: 12px;
  height: 12px;
}

.checkbox--small .checkbox-icon {
  width: 10px;
  height: 10px;
}

.checkbox--large .checkbox-icon {
  width: 14px;
  height: 14px;
}

.checkbox-label {
  font-size: 16px;
  color: #1C1C1E;
}

.checkbox--small .checkbox-label {
  font-size: 14px;
}

.checkbox--large .checkbox-label {
  font-size: 18px;
}

.checkbox--disabled .checkbox-label {
  color: #8E8E93;
}
</style>
